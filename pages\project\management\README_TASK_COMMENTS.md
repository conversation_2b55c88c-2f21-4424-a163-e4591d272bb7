# 📋 คู่มือการใช้งาน Task Activity Log & Comments System

## 🎯 ภาพรวมระบบ

ระบบ **Task Activity Log & Comments** เป็นกระดานแชทสนทนาสำหรับแต่ละ Task ที่ให้สมาชิกในโครงการสามารถ:
- 💬 แสดงความคิดเห็น แลกเปลี่ยนข้อมูล
- 📎 แนบไฟล์ได้หลายไฟล์ (รูปภาพ, PDF, Word, Excel, ZIP, TXT)
- 📊 ติดตามประวัติการอัปเดตงาน
- 🔔 ดู Log การเปลี่ยนแปลงต่างๆ
- 👥 สื่อสารกับทีมงานได้แบบ Real-time

---

## 📦 ไฟล์ที่สร้างขึ้น

### 1. Database Migration
```
/config/migrations/add_task_comments_tables.sql
```
- สร้างตาราง `task_comments` (เก็บความคิดเห็น/Log)
- สร้างตาราง `task_comment_attachments` (เก็บไฟล์แนบ)
- สร้างตาราง `task_mentions` (เก็บการ @ mention)
- สร้าง View `vw_task_comments` (สำหรับ Query ข้อมูลที่ซับซ้อน)

### 2. หน้า Task Detail
```
/pages/project/management/task_detail.php
```
- หน้ารายละเอียด Task แบบกระดานแชท
- แสดงข้อมูลงาน, สถานะ, ความคืบหน้า
- Activity Feed แบบ Real-time
- Form โพสต์ความคิดเห็นพร้อมแนบไฟล์

### 3. API Endpoints
```
/pages/project/management/get_task_comments.php
```
- ดึงรายการความคิดเห็นทั้งหมดของ Task
- แสดงไฟล์แนบ, เวลา, ผู้โพสต์

```
/pages/project/management/post_comment.php
```
- บันทึกความคิดเห็นใหม่
- อัปโหลดไฟล์แนบ (สูงสุด 10 MB ต่อไฟล์)
- สร้าง System Log สำหรับการอัปโหลดไฟล์

```
/pages/project/management/download_attachment.php
```
- ดาวน์โหลดไฟล์แนบ
- ตรวจสอบสิทธิ์การเข้าถึง

### 4. แก้ไขไฟล์เดิม
```
/pages/project/management/get_tasks.php
```
- เพิ่มลิงก์ที่ชื่อ Task ให้คลิกไปหน้า Detail
- เพิ่ม CSS สำหรับ Hover Effect

---

## 🗄️ โครงสร้างฐานข้อมูล

### ตาราง `task_comments`
| Column | Type | Description |
|--------|------|-------------|
| comment_id | CHAR(36) | รหัสความคิดเห็น (UUID) |
| task_id | CHAR(36) | รหัสงาน (FK) |
| project_id | CHAR(36) | รหัสโครงการ (FK) |
| user_id | CHAR(36) | ผู้โพสต์ (FK) |
| comment_text | TEXT | ข้อความ |
| comment_type | ENUM | ประเภท (comment, status_change, file_upload, progress_update, system_log) |
| old_value | VARCHAR(255) | ค่าเดิม (สำหรับ Log) |
| new_value | VARCHAR(255) | ค่าใหม่ (สำหรับ Log) |
| created_at | TIMESTAMP | วันที่โพสต์ |
| updated_at | TIMESTAMP | วันที่แก้ไข |
| is_edited | TINYINT(1) | มีการแก้ไขหรือไม่ |
| is_deleted | TINYINT(1) | ถูกลบหรือไม่ (Soft Delete) |

### ตาราง `task_comment_attachments`
| Column | Type | Description |
|--------|------|-------------|
| attachment_id | CHAR(36) | รหัสไฟล์แนบ (UUID) |
| comment_id | CHAR(36) | รหัสความคิดเห็น (FK) |
| task_id | CHAR(36) | รหัสงาน (FK) |
| file_name | VARCHAR(255) | ชื่อไฟล์ต้นฉบับ |
| file_path | VARCHAR(500) | Path ไฟล์ในระบบ |
| file_size | BIGINT | ขนาดไฟล์ (bytes) |
| file_type | VARCHAR(100) | MIME Type |
| file_extension | VARCHAR(10) | นามสกุลไฟล์ |
| uploaded_by | CHAR(36) | ผู้อัปโหลด (FK) |
| uploaded_at | TIMESTAMP | วันที่อัปโหลด |

### ตาราง `task_mentions` (สำหรับอนาคต)
| Column | Type | Description |
|--------|------|-------------|
| mention_id | CHAR(36) | รหัส mention (UUID) |
| comment_id | CHAR(36) | รหัสความคิดเห็น (FK) |
| task_id | CHAR(36) | รหัสงาน (FK) |
| mentioned_user_id | CHAR(36) | ผู้ถูก mention (FK) |
| mentioned_by | CHAR(36) | ผู้ mention (FK) |
| is_read | TINYINT(1) | อ่านแล้วหรือยัง |
| created_at | TIMESTAMP | วันที่ mention |

---

## 🚀 วิธีการติดตั้ง

### ขั้นตอนที่ 1: Run SQL Migration
1. เปิด phpMyAdmin
2. เลือกฐานข้อมูล `sales_db`
3. ไปที่แท็บ SQL
4. เปิดไฟล์ `/config/migrations/add_task_comments_tables.sql`
5. Copy SQL ทั้งหมดแล้ววาง
6. คลิก "Go" เพื่อรัน

### ขั้นตอนที่ 2: ตรวจสอบโฟลเดอร์ Uploads
```bash
# ตรวจสอบว่าโฟลเดอร์มีอยู่และมี Permission ถูกต้อง
ls -la /mnt/c/xampp/htdocs/sales/uploads/task_attachments
```

### ขั้นตอนที่ 3: ทดสอบระบบ
1. เปิดเบราว์เซอร์ไปที่โครงการ
2. คลิกแถบ "บริหารโครงการ"
3. คลิกที่ชื่อ Task ใดก็ได้
4. จะเข้าสู่หน้า Task Detail พร้อมกระดานแชท

---

## 💡 วิธีการใช้งาน

### 1. เข้าสู่หน้า Task Detail
- จาก Project → แถบ "บริหารโครงการ"
- คลิกที่ **ชื่อ Task** หรือ **รายละเอียด** ในตาราง
- จะเปิดหน้า `task_detail.php` พร้อมกระดานแชท

### 2. แสดงความคิดเห็น
1. พิมพ์ข้อความในกล่องข้อความด้านล่าง
2. (ถ้าต้องการ) คลิกปุ่ม "แนบไฟล์" เพื่อเลือกไฟล์
3. คลิกปุ่ม "โพสต์ความคิดเห็น"

### 3. แนบไฟล์
- รองรับไฟล์: รูปภาพ (JPG, PNG, GIF), PDF, Word, Excel, ZIP, TXT
- ขนาดไฟล์สูงสุด: **10 MB ต่อไฟล์**
- สามารถแนบได้หลายไฟล์ในคราวเดียว

### 4. ดาวน์โหลดไฟล์แนบ
- คลิกที่ไฟล์แนบในความคิดเห็น
- ไฟล์จะถูกดาวน์โหลดอัตโนมัติ

---

## 🎨 ฟีเจอร์หลัก

### ✅ Real-time Activity Feed
- แสดงความคิดเห็นแบบเรียงลำดับเวลา
- Auto-refresh ทุก 30 วินาที
- แสดงเวลาที่ผ่านมา (Time Ago)

### ✅ User Avatar
- แสดงตัวย่อชื่อผู้โพสต์
- สีแตกต่างกันตามชื่อผู้ใช้

### ✅ File Type Icons
- แสดงไอคอนตามประเภทไฟล์
- สีแตกต่างกันตามนามสกุล

### ✅ System Logs
- บันทึกการเปลี่ยนแปลงสถานะ
- บันทึกการอัปเดตความคืบหน้า
- บันทึกการอัปโหลดไฟล์

### ✅ Security
- ตรวจสอบสิทธิ์การเข้าถึง
- CSRF Token Protection
- File Type Validation
- File Size Limitation

---

## 🔐 การตรวจสอบสิทธิ์

ผู้ที่เข้าถึงได้:
1. ✅ **Executive** - เข้าถึงได้ทุก Task
2. ✅ **ผู้สร้าง Task** - เข้าถึง Task ที่สร้างเอง
3. ✅ **ผู้รับผิดชอบ Task** - เข้าถึง Task ที่ถูกมอบหมาย
4. ✅ **สมาชิกในโครงการ** - เข้าถึง Task ในโครงการที่เป็นสมาชิก
5. ✅ **Seller/Owner ของโครงการ** - เข้าถึงทุก Task ในโครงการตัวเอง

---

## 📊 ตัวอย่างการใช้งาน

### กรณีที่ 1: รายงานความคืบหน้า
```
ผู้ใช้: "ทำงานไปได้ 75% แล้ว ยังเหลือแค่การทดสอบ"
[แนบไฟล์: screenshot.png, test_results.pdf]
```

### กรณีที่ 2: สอบถามข้อมูล
```
ผู้ใช้: "อยากทราบว่า spec ของฟีเจอร์นี้เป็นอย่างไร?"
[แนบไฟล์: requirements.docx]
```

### กรณีที่ 3: แจ้งปัญหา
```
ผู้ใช้: "พบ Bug ตอน deploy production"
[แนบไฟล์: error_log.txt, screenshot_error.png]
```

---

## 🛠️ การขยายฟีเจอร์ในอนาคต

### ฟีเจอร์ที่สามารถเพิ่มเติมได้:

1. **@ Mention System**
   - แท็กผู้ใช้ใน Comment
   - ส่ง Notification ไปหาผู้ถูกแท็ก

2. **Rich Text Editor**
   - รองรับ Bold, Italic, Underline
   - รองรับรูปแบบ Markdown

3. **Emoji Reaction**
   - กดไลค์ Comment
   - ใส่ Emoji ตอบกลับ

4. **Edit/Delete Comment**
   - แก้ไข Comment ที่โพสต์ไปแล้ว
   - ลบ Comment (Soft Delete)

5. **Comment Search**
   - ค้นหาความคิดเห็นจากคำค้น
   - Filter ตามวันที่, ผู้โพสต์

6. **Image Preview**
   - แสดงรูปภาพแนบแบบ Thumbnail
   - เปิด Lightbox เมื่อคลิก

7. **Notification Badge**
   - แจ้งเตือนเมื่อมี Comment ใหม่
   - Badge แสดงจำนวนที่ยังไม่ได้อ่าน

---

## 🐛 การแก้ไขปัญหา

### ปัญหา: อัปโหลดไฟล์ไม่สำเร็จ
**สาเหตุ:**
- ขนาดไฟล์เกิน 10 MB
- ประเภทไฟล์ไม่รองรับ
- โฟลเดอร์ไม่มี Permission

**แก้ไข:**
```bash
chmod 755 /mnt/c/xampp/htdocs/sales/uploads/task_attachments
```

### ปัญหา: ไม่แสดง Comments
**สาเหตุ:**
- ยังไม่ Run SQL Migration
- Database connection error

**แก้ไข:**
- ตรวจสอบว่า Run SQL แล้ว
- ตรวจสอบ Error Log ใน Console

### ปัญหา: คลิกชื่อ Task แล้ว 404
**สาเหตุ:**
- Path ไฟล์ไม่ถูกต้อง

**แก้ไข:**
- ตรวจสอบว่าไฟล์ `task_detail.php` อยู่ใน `/pages/project/management/`

---

## 📝 License & Credits

**Developer:** Claude Code
**Date:** 2025-10-02
**Version:** 1.0.0
**Project:** Sales Management System

---

## 📞 Support

หากพบปัญหาหรือต้องการความช่วยเหลือ:
1. ตรวจสอบ Error Log ในเบราว์เซอร์ (F12 → Console)
2. ตรวจสอบ PHP Error Log
3. ติดต่อทีมพัฒนา

---

**🎉 Happy Commenting! 💬**
