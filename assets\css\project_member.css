/* ========================================
   Project Member Tab - Compact UI Design
   ======================================== */

/* Card Styling - Compact */
.member-card {
    border: 1px solid #dee2e6;
    border-radius: 4px;
    box-shadow: none;
    overflow: hidden;
}

/* Card Header - Simple */
.member-card-header {
    background: #007bff;
    color: white;
    padding: 0.75rem 1rem;
    border: none;
    display: flex;
    justify-content: flex-start;
    align-items: center;
}

.member-card-header h3 {
    margin: 0;
    font-size: 0.95rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    flex: 1;
}

.member-card-header i {
    margin-right: 0.5rem;
    font-size: 0.95rem;
}

.btn-add-member {
    background: white;
    border: 1px solid white;
    color: #007bff;
    padding: 0.25rem 0.75rem;
    border-radius: 4px;
    font-weight: 500;
    font-size: 0.85rem;
    transition: all 0.2s ease;
    margin-left: auto;
}

.btn-add-member:hover {
    background: #f8f9fa;
    color: #0056b3;
}

.btn-add-member i {
    font-size: 0.85rem;
}

/* Table Styling - Compact */
.members-table-wrapper {
    padding: 1rem;
    background: white;
}

#membersTable {
    border-collapse: collapse;
    font-size: 0.875rem;
}

#membersTable thead {
    background: #f8f9fa;
}

#membersTable thead th {
    border: 1px solid #dee2e6;
    padding: 0.5rem 0.75rem;
    font-weight: 600;
    color: #495057;
    font-size: 0.8rem;
    border-bottom: 2px solid #dee2e6;
}

#membersTable tbody tr {
    transition: background 0.15s ease;
    border-bottom: 1px solid #dee2e6;
}

#membersTable tbody tr:hover {
    background: #f8f9ff;
}

#membersTable tbody td {
    padding: 0.5rem 0.75rem;
    vertical-align: middle;
    color: #495057;
    border: 1px solid #dee2e6;
}

/* Badge Styling - Simple */
.badge-access {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-weight: 600;
    font-size: 0.7rem;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.badge-access i {
    font-size: 0.65rem;
}

.badge-full-access {
    background: #28a745;
    color: white;
}

.badge-half-access {
    background: #ffc107;
    color: #212529;
}

.badge-view-only {
    background: #17a2b8;
    color: white;
}

/* Role Badge - Compact */
.role-badge {
    background: #e9ecef;
    color: #495057;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-weight: 500;
    font-size: 0.8rem;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.role-badge i {
    font-size: 0.75rem;
}

/* Action Buttons - Simple */
.btn-action {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    border: none;
    transition: all 0.2s ease;
    font-size: 0.8rem;
    margin: 0 0.1rem;
}

.btn-edit {
    background: #007bff;
    color: white;
}

.btn-edit:hover {
    background: #0056b3;
}

.btn-delete {
    background: #dc3545;
    color: white;
}

.btn-delete:hover {
    background: #c82333;
}

/* Member Info - Compact */
.member-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.member-avatar {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: #007bff;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.75rem;
    text-transform: uppercase;
    flex-shrink: 0;
}

.member-name {
    font-weight: 500;
    color: #212529;
    font-size: 0.875rem;
}

/* Date Display - Compact */
.date-text {
    color: #6c757d;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.date-text i {
    color: #adb5bd;
    font-size: 0.75rem;
}

/* Modal Styling - Simple */
.modal-content {
    border: none;
    border-radius: 6px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.modal-header {
    background: #007bff;
    color: white;
    border: none;
    border-radius: 6px 6px 0 0;
    padding: 1rem 1.25rem;
}

.modal-header .modal-title {
    font-weight: 600;
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.modal-header .modal-title i {
    font-size: 1rem;
}

.modal-header .close {
    color: white;
    opacity: 0.9;
    text-shadow: none;
}

.modal-header .close:hover {
    opacity: 1;
}

.modal-body {
    padding: 1.25rem;
}

.modal-footer {
    border-top: 1px solid #dee2e6;
    padding: 0.75rem 1.25rem;
}

/* Form Styling - Compact */
.form-group label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
}

.form-group label i {
    font-size: 0.875rem;
    color: #667eea;
}

.form-control {
    border: 1px solid #ced4da;
    border-radius: 4px;
    padding: 0.5rem 0.75rem;
    transition: all 0.15s ease;
    font-size: 0.875rem;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.15);
}

.form-text {
    font-size: 0.75rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

.form-text i {
    font-size: 0.7rem;
}

.select2-container--bootstrap4 .select2-selection {
    border: 1px solid #ced4da;
    border-radius: 4px;
    min-height: 38px;
}

.select2-container--bootstrap4 .select2-selection:focus {
    border-color: #667eea;
}

/* Alert - Compact */
.alert {
    padding: 0.75rem 1rem;
    margin-top: 1rem;
    font-size: 0.85rem;
}

.alert ul {
    margin-bottom: 0;
    padding-left: 1.25rem;
    font-size: 0.8rem;
}

.alert li {
    margin-bottom: 0.25rem;
}

/* Button Styling - Simple */
.btn-primary {
    background: #007bff;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-weight: 500;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.btn-primary:hover {
    background: #0056b3;
}

.btn-primary i {
    font-size: 0.85rem;
}

.btn-secondary {
    background: #6c757d;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-weight: 500;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.btn-secondary:hover {
    background: #5a6268;
    color: white;
}

.btn-secondary i {
    font-size: 0.85rem;
}

/* Empty State - Compact */
.empty-state {
    text-align: center;
    padding: 2rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 0.75rem;
    opacity: 0.4;
}

.empty-state p {
    font-size: 0.95rem;
    margin-bottom: 1rem;
}

/* Responsive */
@media (max-width: 768px) {
    .member-card-header {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }

    .btn-add-member {
        width: 100%;
    }

    .member-info {
        flex-direction: row;
        justify-content: flex-start;
    }

    #membersTable {
        font-size: 0.8rem;
    }

    .badge-access {
        font-size: 0.65rem;
        padding: 0.2rem 0.4rem;
    }

    .btn-action {
        padding: 0.2rem 0.4rem;
        font-size: 0.75rem;
    }
}

/* DataTables Custom Styling - Compact */
.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter {
    margin-bottom: 0.75rem;
}

.dataTables_wrapper .dataTables_filter input {
    border: 1px solid #ced4da;
    border-radius: 4px;
    padding: 0.375rem 0.75rem;
    margin-left: 0.5rem;
    font-size: 0.875rem;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
    border-radius: 4px;
    margin: 0 2px;
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
    background: #007bff !important;
    border-color: #007bff !important;
    color: white !important;
}

/* DataTables Buttons - Same Color */
.dt-buttons .btn {
    background: #6c757d !important;
    border-color: #6c757d !important;
    color: white !important;
    font-size: 0.875rem !important;
    padding: 0.375rem 0.75rem !important;
    margin-right: 0.25rem !important;
}

.dt-buttons .btn:hover {
    background: #5a6268 !important;
    border-color: #545b62 !important;
}

/* Tooltip */
.tooltip-inner {
    background: #212529;
    border-radius: 4px;
    padding: 0.375rem 0.75rem;
    font-size: 0.8rem;
}
