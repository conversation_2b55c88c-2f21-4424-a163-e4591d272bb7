# คู่มือการเปลี่ยนชื่อโฟลเดอร์โปรเจค (Project Root Name Change Guide)

เมื่อคุณต้องการเปลี่ยนชื่อโฟลเดอร์โปรเจคจาก `sales` เป็นชื่ออื่น คุณจะต้องแก้ไขไฟล์และการตั้งค่าในจุดต่อไปนี้:

## 🎯 จุดที่ต้องแก้ไขหลัก (Core Files)

### 1. ไฟล์ Environment Variables

#### `.env`
```env
# เปลี่ยนจาก /sales/ เป็นชื่อโฟลเดอร์ใหม่
BASE_URL=/NEW_FOLDER_NAME/

# หากต้องการเปลี่ยนชื่อฐานข้อมูลด้วย
DB_NAME=new_database_name
```

#### `.env.example`
```env
# เปลี่ยนจาก /sales/ เป็นชื่อโฟลเดอร์ใหม่
BASE_URL=/NEW_FOLDER_NAME/
```

### 2. ไฟล์ Fallback ใน config/condb.php
```php
// แก้ไข fallback value ใน line 7
define('BASE_URL', getEnvVar('BASE_URL', '/NEW_FOLDER_NAME/'));

// แก้ไข fallback database name ใน line 11 (ถ้าต้องการ)
$dbname = getEnvVar('DB_NAME', 'new_database_name');
```

## 📁 ระบบไฟล์และโฟลเดอร์

### 3. เปลี่ยนชื่อโฟลเดอร์ฟิสิคัล
```bash
# ใน xampp/htdocs/
mv sales NEW_FOLDER_NAME
```

### 4. การตั้งค่า Web Server

#### Virtual Host (หากใช้)
```apache
DocumentRoot "C:/xampp/htdocs/NEW_FOLDER_NAME"
<Directory "C:/xampp/htdocs/NEW_FOLDER_NAME">
```

#### หรือใช้ URL ใหม่
```
http://localhost/NEW_FOLDER_NAME/
```

## 💾 ฐานข้อมูล

### 5. ชื่อฐานข้อมูล (ถ้าต้องการเปลี่ยน)

#### สร้างฐานข้อมูลใหม่
```sql
CREATE DATABASE new_database_name;
```

#### นำเข้าข้อมูล
```bash
mysql -u root -p new_database_name < config/sale_db.sql
```

#### หรือเปลี่ยนชื่อฐานข้อมูลเดิม
```sql
CREATE DATABASE new_database_name;
RENAME TABLE sales_db.table_name TO new_database_name.table_name;
# ทำกับทุกตาราง
```

## 🔍 ไฟล์ที่ใช้ BASE_URL (ไม่ต้องแก้)

ไฟล์เหล่านี้ใช้ constant `BASE_URL` ดังนั้นจะอัพเดทอัตโนมัติเมื่อแก้ไข `.env`:

### ไฟล์หลัก
- `index.php`
- `login.php`
- `templated.php`
- `test_env.php`

### ไฟล์ Include
- `include/Header.php`
- `include/Footer.php`
- `include/Navbar.php`
- `include/Add_session.php`

### ไฟล์ Pages (ตัวอย่าง)
- `pages/*//*.php` - ไฟล์ทั้งหมดในโฟลเดอร์ pages
- `pages/claims/add_claim.php`
- `pages/customer/view_customer.php`
- `pages/setting/suppliers/view_supplier.php`
- และอีกมากมาย...

## ✅ ขั้นตอนการดำเนินการ

### Step 1: ปิดระบบ
```bash
# หยุด Apache/Nginx และ MySQL
```

### Step 2: สำรองข้อมูล
```bash
# สำรอง Database
mysqldump -u root -p sales_db > backup_sales_db.sql

# สำรอง Files
cp -r sales sales_backup
```

### Step 3: เปลี่ยนชื่อโฟลเดอร์
```bash
mv sales NEW_FOLDER_NAME
```

### Step 4: แก้ไขไฟล์การตั้งค่า
1. แก้ไข `.env`
2. แก้ไข `.env.example`
3. แก้ไข `config/condb.php` fallback values

### Step 5: ตั้งค่าฐานข้อมูล (ถ้าเปลี่ยนชื่อ)
```sql
CREATE DATABASE new_database_name;
# นำเข้าข้อมูล หรือ เปลี่ยนชื่อตาราง
```

### Step 6: ทดสอบระบบ
```bash
# เข้าไปที่
http://localhost/NEW_FOLDER_NAME/test_env.php
```

### Step 7: ทำความสะอาด
```bash
# ลบไฟล์ทดสอบ
rm NEW_FOLDER_NAME/test_env.php

# ลบ backup (หากต้องการ)
rm -rf sales_backup
```

## ⚠️ ข้อควรระวัง

1. **สำรองข้อมูลก่อนเสมอ** - ทั้งไฟล์และฐานข้อมูล
2. **ตรวจสอบ URL paths** - ใน CSS/JS files หากมี absolute paths
3. **Session data** - อาจต้อง logout/login ใหม่
4. **Upload files** - ตรวจสอบ path ใน uploads/
5. **Log files** - ตรวจสอบ path ใน logs/

## 🧪 การทดสอบ

หลังจากเปลี่ยนชื่อแล้ว ทดสอบ:

1. **หน้าแรก**: `http://localhost/NEW_FOLDER_NAME/`
2. **ลอกอิน**: `http://localhost/NEW_FOLDER_NAME/login.php`
3. **Assets**: ตรวจสอบ CSS, JS, รูปภาพโหลดได้ปกติ
4. **Database**: ตรวจสอบการเชื่อมต่อและดึงข้อมูล
5. **Upload**: ทดสอบการอัพโหลดไฟล์
6. **Navigation**: ทดสอบลิงก์ทั้งหมดในระบบ

## 📝 ตัวอย่างการเปลี่ยนชื่อ

หากต้องการเปลี่ยนจาก `sales` เป็น `crm`:

```env
# ใน .env
BASE_URL=/crm/
DB_NAME=crm_db
```

```php
// ใน config/condb.php
define('BASE_URL', getEnvVar('BASE_URL', '/crm/'));
$dbname = getEnvVar('DB_NAME', 'crm_db');
```

```bash
# Command line
mv sales crm
mysql -u root -p -e "CREATE DATABASE crm_db;"
mysql -u root -p crm_db < config/sale_db.sql
```

---

**หมายเหตุ**: ไฟล์นี้สร้างขึ้นเพื่อเป็นคู่มือการเปลี่ยนชื่อโฟลเดอร์โปรเจค หลังจากใช้งานเสร็จแล้วสามารถลบได้